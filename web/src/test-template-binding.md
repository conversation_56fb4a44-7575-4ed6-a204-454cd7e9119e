# 模板数据双向绑定修复说明

## 问题描述
编辑模版时，从接口加载的数据中，文本跑马灯的滚动文本、文字大小、滚动速度、背景颜色等属性无法正常修改。新建模版时可以正常操作，怀疑是数据结构问题导致的单向传递。

## 问题原因分析
1. **事件绑定问题**：子组件中使用了 `@change` 事件，但某些控件（如 `el-input`）需要使用 `@input` 事件才能实时响应
2. **颜色选择器事件参数问题**：`el-color-picker` 的 `@change` 事件参数 `$event` 可能为空，需要直接使用 `v-model` 绑定的值
3. **数据加载时缺少默认值**：从接口加载的文本跑马灯数据可能缺少必要的属性，导致绑定失败
4. **响应式更新问题**：父组件的 `handleUpdateMaterialProperty` 方法需要强制触发响应式更新
5. **编辑状态初始化问题**：编辑模板时没有正确重置高亮索引

## 修复内容

### 1. Template.vue 修复

#### 1.1 增强 `handleUpdateMaterialProperty` 方法
```javascript
// 添加详细日志
console.log('handleUpdateMaterialProperty called:', { clientKey, key, value });

// 强制更新当前页面的 materialList 以确保响应式
const currentMaterialList = [...this.templatePages[this.currentPage].materialList];
this.$set(this.templatePages[this.currentPage], 'materialList', currentMaterialList);

// 触发画板刷新
this.templateAreaRefreshKey++;

// 确保属性面板也能及时更新
this.$nextTick(() => {
  if (this.$refs.propertiesPanel) {
    if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
      this.$refs.propertiesPanel.refreshCurrentMaterialData();
    }
  }
});
```

#### 1.2 数据加载时添加文本跑马灯默认属性
```javascript
// 为文本跑马灯添加默认属性（如果缺失）
if (item.template_sm_type === 6) {
  if (!item.marquee_text) {
    this.$set(item, 'marquee_text', '请输入滚动文本');
  }
  if (!item.font_size) {
    this.$set(item, 'font_size', 24);
  }
  if (!item.scroll_speed) {
    this.$set(item, 'scroll_speed', 50);
  }
  if (!item.font_color) {
    this.$set(item, 'font_color', '#000000');
  }
  if (!item.background_color) {
    this.$set(item, 'background_color', '#ffffff');
  }
}
```

#### 1.3 数据加载完成后强制刷新属性面板
```javascript
// 强制刷新属性面板以确保数据正确绑定
this.$nextTick(() => {
  if (this.$refs.propertiesPanel) {
    if (this.$refs.propertiesPanel.refreshCurrentMaterialData) {
      this.$refs.propertiesPanel.refreshCurrentMaterialData();
    }
    this.$refs.propertiesPanel.$forceUpdate();
  }
  // 强制更新整个组件
  this.$forceUpdate();
});
```

#### 1.4 编辑模板时重置高亮索引
```javascript
edit (row) {
  if (row.type === 1) {
    this.id = row.id;
    this.getDetail(row.id);
    this.isEdit = true;
    this.isShowTemplate = true;

    // 重置高亮索引，确保属性面板正确显示
    this.highlightedMaterialIndex = -1;
  }
}
```

### 2. TemplatePropertiesPanel.vue 修复

#### 2.1 修改文本输入框事件绑定
```html
<!-- 从 @change 改为 @input -->
<el-input type="textarea" v-model="selectedMaterial.marquee_text"
    @input="updateMaterialProperty('marquee_text', $event)" :rows="3"
    style="width: 100%;"></el-input>
```

#### 2.2 修改位置和尺寸输入框事件绑定
```html
<!-- 从 @change 改为 @input，并使用绑定值而不是 $event -->
<el-input v-model.number="selectedMaterial.x_axis"
    @input="updateMaterialProperty('x_axis', selectedMaterial.x_axis)" size="mini"
    style="width: 100%;"></el-input>
```

#### 2.3 修复颜色选择器事件参数
```html
<!-- 直接使用绑定值而不是 $event -->
<el-color-picker v-model="selectedMaterial.font_color"
    @change="updateMaterialProperty('font_color', selectedMaterial.font_color)" size="mini"
    style="width: 100%;"></el-color-picker>
```

#### 2.4 修复数字输入框事件参数
```html
<!-- 直接使用绑定值而不是 $event -->
<el-input-number v-model="selectedMaterial.font_size"
    @change="updateMaterialProperty('font_size', selectedMaterial.font_size)" size="mini" :min="8"
    :max="100" style="width: 100%;" controls-position="right"></el-input-number>
```

#### 2.5 增强 `updateMaterialProperty` 方法
```javascript
updateMaterialProperty (key, value) {
    if (!this.selectedMaterial) return;

    console.log('TemplatePropertiesPanel updateMaterialProperty:', { key, value, clientKey: this.selectedMaterial.clientKey });

    // 立即更新本地数据以确保UI响应
    this.$set(this.selectedMaterial, key, value);

    // 发送事件给父组件
    this.$emit('update-material-property', {
        clientKey: this.selectedMaterial.clientKey,
        key: key,
        value: value
    });

    // 触发刷新以确保视图更新
    this.refreshTrigger++;
}
```

## 测试步骤
1. 创建一个新的文本跑马灯模板
2. 设置各种属性值
3. 保存模板
4. 重新编辑该模板
5. 验证以下属性是否可以正常修改：
   - 滚动文本内容
   - 字体大小
   - 滚动速度
   - 字体颜色
   - 背景颜色
   - 位置（左、上）
   - 尺寸（宽、高）

## 预期结果
所有属性都应该能够正常修改，并且修改后的值能够正确保存到父组件的数据结构中。控制台应该显示相关的调试日志，确认数据更新流程正常工作。

## 关键修复点总结
1. **事件类型**：输入框使用 `@input` 而不是 `@change`
2. **事件参数**：颜色选择器和数字输入框使用绑定值而不是 `$event`
3. **默认值**：确保从接口加载的数据有完整的属性
4. **响应式更新**：使用 `$set` 和强制刷新确保数据变化能被正确检测
5. **状态重置**：编辑时重置高亮索引避免状态混乱
6. **计算属性**：将 `getCurrentHighlightedMaterial` 改为计算属性 `currentHighlightedMaterial` 确保响应式
7. **避免循环更新**：简化数据更新流程，避免子组件和父组件之间的循环刷新

## 最新修复（第二轮）

### 问题分析
从控制台日志发现存在过度刷新的问题，每次输入都会触发多次更新和刷新，这可能导致性能问题和状态混乱。

### 修复内容

#### 1. 简化数据更新流程
- 移除子组件中的立即本地数据更新，只发送事件给父组件
- 简化父组件的 `handleUpdateMaterialProperty` 方法，移除过度的刷新操作
- 将 `getCurrentHighlightedMaterial` 方法改为计算属性 `currentHighlightedMaterial`

#### 2. 修复文本输入框事件参数
- 将文本输入框的 `@input` 事件参数从 `$event` 改为 `selectedMaterial.marquee_text`

#### 3. 增加调试日志
- 在 watch 中添加详细的调试日志，帮助跟踪数据变化

## 测试验证
现在请重新测试：
1. 编辑现有的文本跑马灯模板
2. 修改滚动文本内容
3. 观察控制台日志，应该看到更简洁的更新流程
4. 验证所有属性修改是否正常工作且没有过度刷新
